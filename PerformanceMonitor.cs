using System.Diagnostics;
using System.Collections.Concurrent;

namespace LeadHIstoryMigrationNew
{
    /// <summary>
    /// Performance monitoring utility for tracking migration performance
    /// </summary>
    public static class PerformanceMonitor
    {
        private static readonly ConcurrentDictionary<string, PerformanceMetrics> _metrics = new();
        private static readonly object _lockObject = new object();

        public class PerformanceMetrics
        {
            public string Operation { get; set; } = string.Empty;
            public long TotalExecutions { get; set; }
            public long TotalMilliseconds { get; set; }
            public long MinMilliseconds { get; set; } = long.MaxValue;
            public long MaxMilliseconds { get; set; }
            public double AverageMilliseconds => TotalExecutions > 0 ? (double)TotalMilliseconds / TotalExecutions : 0;
            public long TotalRecordsProcessed { get; set; }
            public double RecordsPerSecond => TotalMilliseconds > 0 ? (TotalRecordsProcessed * 1000.0) / TotalMilliseconds : 0;
        }

        /// <summary>
        /// Measures the execution time of an operation
        /// </summary>
        public static async Task<T> MeasureAsync<T>(string operationName, Func<Task<T>> operation, long recordCount = 0)
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                var result = await operation();
                stopwatch.Stop();
                RecordMetric(operationName, stopwatch.ElapsedMilliseconds, recordCount);
                return result;
            }
            catch
            {
                stopwatch.Stop();
                RecordMetric(operationName, stopwatch.ElapsedMilliseconds, recordCount);
                throw;
            }
        }

        /// <summary>
        /// Measures the execution time of a synchronous operation
        /// </summary>
        public static T Measure<T>(string operationName, Func<T> operation, long recordCount = 0)
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                var result = operation();
                stopwatch.Stop();
                RecordMetric(operationName, stopwatch.ElapsedMilliseconds, recordCount);
                return result;
            }
            catch
            {
                stopwatch.Stop();
                RecordMetric(operationName, stopwatch.ElapsedMilliseconds, recordCount);
                throw;
            }
        }

        /// <summary>
        /// Records a performance metric
        /// </summary>
        private static void RecordMetric(string operationName, long milliseconds, long recordCount)
        {
            _metrics.AddOrUpdate(operationName,
                new PerformanceMetrics
                {
                    Operation = operationName,
                    TotalExecutions = 1,
                    TotalMilliseconds = milliseconds,
                    MinMilliseconds = milliseconds,
                    MaxMilliseconds = milliseconds,
                    TotalRecordsProcessed = recordCount
                },
                (key, existing) =>
                {
                    lock (_lockObject)
                    {
                        existing.TotalExecutions++;
                        existing.TotalMilliseconds += milliseconds;
                        existing.MinMilliseconds = Math.Min(existing.MinMilliseconds, milliseconds);
                        existing.MaxMilliseconds = Math.Max(existing.MaxMilliseconds, milliseconds);
                        existing.TotalRecordsProcessed += recordCount;
                        return existing;
                    }
                });
        }

        /// <summary>
        /// Gets all performance metrics
        /// </summary>
        public static IReadOnlyDictionary<string, PerformanceMetrics> GetMetrics()
        {
            return _metrics.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        }

        /// <summary>
        /// Prints performance summary to console
        /// </summary>
        public static void PrintSummary()
        {
            Console.WriteLine("\n=== Performance Summary ===");
            Console.WriteLine($"{"Operation",-30} {"Executions",-12} {"Avg (ms)",-10} {"Min (ms)",-10} {"Max (ms)",-10} {"Records/sec",-12}");
            Console.WriteLine(new string('-', 90));

            foreach (var metric in _metrics.Values.OrderByDescending(m => m.TotalMilliseconds))
            {
                Console.WriteLine($"{metric.Operation,-30} {metric.TotalExecutions,-12} {metric.AverageMilliseconds,-10:F2} {metric.MinMilliseconds,-10} {metric.MaxMilliseconds,-10} {metric.RecordsPerSecond,-12:F0}");
            }
            Console.WriteLine(new string('-', 90));
        }

        /// <summary>
        /// Clears all metrics
        /// </summary>
        public static void Clear()
        {
            _metrics.Clear();
        }

        /// <summary>
        /// Gets memory usage information
        /// </summary>
        public static (long WorkingSet, long PrivateMemory, long GCMemory) GetMemoryUsage()
        {
            var process = Process.GetCurrentProcess();
            return (
                WorkingSet: process.WorkingSet64,
                PrivateMemory: process.PrivateMemorySize64,
                GCMemory: GC.GetTotalMemory(false)
            );
        }

        /// <summary>
        /// Prints memory usage information
        /// </summary>
        public static void PrintMemoryUsage()
        {
            var memory = GetMemoryUsage();
            Console.WriteLine($"\n=== Memory Usage ===");
            Console.WriteLine($"Working Set: {memory.WorkingSet / 1024 / 1024:N0} MB");
            Console.WriteLine($"Private Memory: {memory.PrivateMemory / 1024 / 1024:N0} MB");
            Console.WriteLine($"GC Memory: {memory.GCMemory / 1024 / 1024:N0} MB");
            Console.WriteLine($"GC Collections - Gen0: {GC.CollectionCount(0)}, Gen1: {GC.CollectionCount(1)}, Gen2: {GC.CollectionCount(2)}");
        }
    }
}
