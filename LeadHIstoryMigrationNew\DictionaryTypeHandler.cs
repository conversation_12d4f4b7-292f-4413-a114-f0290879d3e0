﻿using Dapper;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace LeadHIstoryMigrationNew
{
    public class DictionaryIntStringTypeHandler : SqlMapper.TypeHandler<IDictionary<int, string>>
    {
        public override IDictionary<int, string>? Parse(object value)
        {
            if (value == null || value is DBNull)
                return null;

            var json = value.ToString();
            if (string.IsNullOrWhiteSpace(json))
                return null;

            try
            {
                return JsonSerializer.Deserialize<Dictionary<int, string>>(json);
            }
            catch
            {
                return null;
            }
        }

        public override void SetValue(IDbDataParameter parameter, IDictionary<int, string>? value)
        {
            parameter.Value = value == null
                ? (object)DBNull.Value
                : JsonSerializer.Serialize(value);
        }
    }

    public class DictionaryIntGuidTypeHandler : SqlMapper.TypeHandler<IDictionary<int, Guid>>
    {
        public override IDictionary<int, Guid>? Parse(object value)
        {
            if (value == null || value is DBNull)
                return null;

            var json = value.ToString();
            if (string.IsNullOrWhiteSpace(json))
                return null;

            try
            {
                return JsonSerializer.Deserialize<Dictionary<int, Guid>>(json);
            }
            catch
            {
                return null;
            }
        }

        public override void SetValue(IDbDataParameter parameter, IDictionary<int, Guid>? value)
        {
            parameter.Value = value == null
                ? (object)DBNull.Value
                : JsonSerializer.Serialize(value);
        }
    }

    public class DictionaryIntLongTypeHandler : SqlMapper.TypeHandler<IDictionary<int, long>>
    {
        public override IDictionary<int, long>? Parse(object value)
        {
            if (value == null || value is DBNull)
                return null;

            var json = value.ToString();
            if (string.IsNullOrWhiteSpace(json))
                return null;

            try
            {
                return JsonSerializer.Deserialize<Dictionary<int, long>>(json);
            }
            catch
            {
                return null;
            }
        }

        public override void SetValue(IDbDataParameter parameter, IDictionary<int, long>? value)
        {
            parameter.Value = value == null
                ? (object)DBNull.Value
                : JsonSerializer.Serialize(value);
        }
    }

    public class DictionaryIntDoubleTypeHandler : SqlMapper.TypeHandler<IDictionary<int, double>>
    {
        public override IDictionary<int, double>? Parse(object value)
        {
            if (value == null || value is DBNull)
                return null;

            var json = value.ToString();
            if (string.IsNullOrWhiteSpace(json))
                return null;

            try
            {
                return JsonSerializer.Deserialize<Dictionary<int, double>>(json);
            }
            catch
            {
                return null;
            }
        }

        public override void SetValue(IDbDataParameter parameter, IDictionary<int, double>? value)
        {
            parameter.Value = value == null
                ? (object)DBNull.Value
                : JsonSerializer.Serialize(value);
        }
    }

    public class DictionaryIntFloatTypeHandler : SqlMapper.TypeHandler<IDictionary<int, float>>
    {
        public override IDictionary<int, float>? Parse(object value)
        {
            if (value == null || value is DBNull)
                return null;

            var json = value.ToString();
            if (string.IsNullOrWhiteSpace(json))
                return null;

            try
            {
                return JsonSerializer.Deserialize<Dictionary<int, float>>(json);
            }
            catch
            {
                return null;
            }
        }

        public override void SetValue(IDbDataParameter parameter, IDictionary<int, float>? value)
        {
            parameter.Value = value == null
                ? (object)DBNull.Value
                : JsonSerializer.Serialize(value);
        }
    }

    public class DictionaryIntIntTypeHandler : SqlMapper.TypeHandler<IDictionary<int, int>>
    {
        public override IDictionary<int, int>? Parse(object value)
        {
            if (value == null || value is DBNull)
                return null;

            var json = value.ToString();
            if (string.IsNullOrWhiteSpace(json))
                return null;

            try
            {
                return JsonSerializer.Deserialize<Dictionary<int, int>>(json);
            }
            catch
            {
                return null;
            }
        }

        public override void SetValue(IDbDataParameter parameter, IDictionary<int, int>? value)
        {
            parameter.Value = value == null
                ? (object)DBNull.Value
                : JsonSerializer.Serialize(value);
        }
    }

    public class DictionaryIntBoolTypeHandler : SqlMapper.TypeHandler<IDictionary<int, bool>>
    {
        public override IDictionary<int, bool>? Parse(object value)
        {
            if (value == null || value is DBNull)
                return null;

            var json = value.ToString();
            if (string.IsNullOrWhiteSpace(json))
                return null;

            try
            {
                return JsonSerializer.Deserialize<Dictionary<int, bool>>(json);
            }
            catch
            {
                return null;
            }
        }

        public override void SetValue(IDbDataParameter parameter, IDictionary<int, bool>? value)
        {
            parameter.Value = value == null
                ? (object)DBNull.Value
                : JsonSerializer.Serialize(value);
        }
    }

    public class DictionaryIntDateTimeTypeHandler : SqlMapper.TypeHandler<IDictionary<int, DateTime>>
    {
        public override IDictionary<int, DateTime>? Parse(object value)
        {
            if (value == null || value is DBNull)
                return null;

            var json = value.ToString();
            if (string.IsNullOrWhiteSpace(json))
                return null;

            try
            {
                return JsonSerializer.Deserialize<Dictionary<int, DateTime>>(json);
            }
            catch
            {
                return null;
            }
        }

        public override void SetValue(IDbDataParameter parameter, IDictionary<int, DateTime>? value)
        {
            parameter.Value = value == null
                ? (object)DBNull.Value
                : JsonSerializer.Serialize(value);
        }
    }

    public class DictionaryIntNullableDateTimeTypeHandler : SqlMapper.TypeHandler<IDictionary<int, DateTime?>>
    {
        public override IDictionary<int, DateTime?>? Parse(object value)
        {
            if (value == null || value is DBNull)
                return null;

            var json = value.ToString();
            if (string.IsNullOrWhiteSpace(json))
                return null;

            try
            {
                return JsonSerializer.Deserialize<Dictionary<int, DateTime?>>(json);
            }
            catch
            {
                return null;
            }
        }

        public override void SetValue(IDbDataParameter parameter, IDictionary<int, DateTime?>? value)
        {
            parameter.Value = value == null
                ? (object)DBNull.Value
                : JsonSerializer.Serialize(value);
        }
    }

    // Enum Type Handlers
    public class DictionaryIntEnumTypeHandler<TEnum> : SqlMapper.TypeHandler<IDictionary<int, TEnum>> where TEnum : struct, Enum
    {
        public override IDictionary<int, TEnum>? Parse(object value)
        {
            if (value == null || value is DBNull)
                return null;

            var json = value.ToString();
            if (string.IsNullOrWhiteSpace(json))
                return null;

            try
            {
                return JsonSerializer.Deserialize<Dictionary<int, TEnum>>(json);
            }
            catch
            {
                return null;
            }
        }

        public override void SetValue(IDbDataParameter parameter, IDictionary<int, TEnum>? value)
        {
            parameter.Value = value == null
                ? (object)DBNull.Value
                : JsonSerializer.Serialize(value);
        }
    }
}
