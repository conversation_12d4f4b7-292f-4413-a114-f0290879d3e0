using System.Collections.Concurrent;
using LeadHIstoryMigrationNew.Models;

namespace LeadHIstoryMigrationNew
{
    /// <summary>
    /// Centralized cache manager for frequently accessed data during migration
    /// </summary>
    public static class CacheManager
    {
        // Cache for tenant information
        private static readonly ConcurrentDictionary<string, Tenants> _tenantCache = new();
        
        // Cache for database connection strings per tenant
        private static readonly ConcurrentDictionary<string, string> _connectionStringCache = new();
        
        // Cache for lead counts per tenant (for capacity estimation)
        private static readonly ConcurrentDictionary<string, int> _leadCountCache = new();

        /// <summary>
        /// Gets or adds tenant information to cache
        /// </summary>
        public static Tenants GetOrAddTenant(string tenantId, Func<string, Tenants> factory)
        {
            return _tenantCache.GetOrAdd(tenantId, factory);
        }

        /// <summary>
        /// Gets or adds connection string to cache
        /// </summary>
        public static string GetOrAddConnectionString(string tenantId, Func<string, string> factory)
        {
            return _connectionStringCache.GetOrAdd(tenantId, factory);
        }

        /// <summary>
        /// Gets or adds lead count for capacity estimation
        /// </summary>
        public static int GetOrAddLeadCount(string tenantId, Func<string, int> factory)
        {
            return _leadCountCache.GetOrAdd(tenantId, factory);
        }

        /// <summary>
        /// Clears all caches - useful for testing or memory management
        /// </summary>
        public static void ClearAllCaches()
        {
            _tenantCache.Clear();
            _connectionStringCache.Clear();
            _leadCountCache.Clear();
        }

        /// <summary>
        /// Gets cache statistics for monitoring
        /// </summary>
        public static (int TenantCount, int ConnectionStringCount, int LeadCountCount) GetCacheStats()
        {
            return (_tenantCache.Count, _connectionStringCache.Count, _leadCountCache.Count);
        }
    }
}
