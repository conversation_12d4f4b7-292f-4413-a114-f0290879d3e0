{"version": 2, "dgSpecHash": "zybVPhJKYEw=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\Desktop\\My Project\\Lead History Migration New\\LeadHIstoryMigrationNew\\LeadHIstoryMigrationNew.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\dapper\\2.1.66\\dapper.2.1.66.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mapster\\7.4.0\\mapster.7.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mapster.core\\1.2.1\\mapster.core.1.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\8.0.2\\microsoft.extensions.dependencyinjection.abstractions.8.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\8.0.2\\microsoft.extensions.logging.abstractions.8.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\npgsql\\9.0.3\\npgsql.9.0.3.nupkg.sha512"], "logs": []}