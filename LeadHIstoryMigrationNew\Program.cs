﻿using Dapper;
using LeadHIstoryMigrationNew.Existing_Lead_Hisotry.Models;
using LeadHIstoryMigrationNew.Models;
using Npgsql;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace LeadHIstoryMigrationNew
{
    public class Program
    {
        public const string ConnectionString = "Host=lrb-qa-new.postgres.database.azure.com;Port=5432;Database=leadratBlackApp;Username=dbmasteruser;Password=********************************;Pooling=true;MinPoolSize=3;MaxPoolSize=5000;";
        
        static async Task Main(string[] args)
        {
            var conn = new NpgsqlConnection(ConnectionString);
            try
            {

                RegisterDapperTypeHandlers();

                string innerQuery = "SELECT * From \"MultiTenancy\".\"Tenants\"";
                await conn.OpenAsync();
                var tenants = (await conn.QueryAsync<Tenants>(innerQuery)).ToList();
                await conn.CloseAsync();
                LeadHistoryMigration migration = new LeadHistoryMigration();
                foreach (var tenant in tenants)
                {
                    if (tenant.Id == "ratify")
                    {
                        Console.WriteLine(tenant.Id);
                        await migration.ProcessLeadHistoryMigration(tenant.Id, ConnectionString);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
        }

        #region SQL Mapper
        private static void RegisterDapperTypeHandlers()
        {
            SqlMapper.AddTypeHandler(new DictionaryIntStringTypeHandler());
            SqlMapper.AddTypeHandler(new DictionaryIntGuidTypeHandler());
            SqlMapper.AddTypeHandler(new DictionaryIntLongTypeHandler());
            SqlMapper.AddTypeHandler(new DictionaryIntDoubleTypeHandler());
            SqlMapper.AddTypeHandler(new DictionaryIntFloatTypeHandler());
            SqlMapper.AddTypeHandler(new DictionaryIntIntTypeHandler());
            SqlMapper.AddTypeHandler(new DictionaryIntBoolTypeHandler());
            SqlMapper.AddTypeHandler(new DictionaryIntDateTimeTypeHandler());
            SqlMapper.AddTypeHandler(new DictionaryIntNullableDateTimeTypeHandler());

            // Register enum handlers
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<EnquiryType>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<SaleType>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<LeadSource>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<ContactType>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<Profession>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<UploadType>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<OfferType>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<FurnishStatus>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<LeadAssignmentType>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<BulkType>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<Purpose>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<PossesionType>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<Gender>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<MaritalStatusType>());
        }
        #endregion
    }
}
