using Dapper;
using LeadHIstoryMigrationNew.Existing_Lead_Hisotry.Models;
using LeadHIstoryMigrationNew.Models;
using Npgsql;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace LeadHIstoryMigrationNew
{
    public class Program
    {
        public const string ConnectionString = "Host=lrb-qa-new.postgres.database.azure.com;Port=5432;Database=leadratBlackApp;Username=dbmasteruser;Password=********************************;Pooling=true;MinPoolSize=3;MaxPoolSize=5000;";
        
        static async Task Main(string[] args)
        {
            var conn = new NpgsqlConnection(ConnectionString);
            try
            {

                RegisterDapperTypeHandlers();

                string innerQuery = "SELECT * From \"MultiTenancy\".\"Tenants\"";
                await conn.OpenAsync();
                var tenants = (await conn.QueryAsync<Tenants>(innerQuery)).ToList();
                await conn.CloseAsync();

                // Cache tenants for potential reuse with null safety
                if (tenants?.Any() == true)
                {
                    foreach (var tenant in tenants.Where(t => t != null && !string.IsNullOrWhiteSpace(t.Id)))
                    {
                        try
                        {
                            CacheManager.GetOrAddTenant(tenant.Id, _ => tenant);
                            CacheManager.GetOrAddConnectionString(tenant.Id, _ => ConnectionString);
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Warning: Error caching tenant {tenant.Id}: {ex.Message}");
                        }
                    }
                }

                LeadHistoryMigration migration = new LeadHistoryMigration();

                // Process tenants in parallel for better performance with null safety
                var validTenants = tenants?
                    .Where(tenant => tenant != null &&
                                   !string.IsNullOrWhiteSpace(tenant.Id) &&
                                   tenant.Id == "ratify") // Filter as needed
                    .ToList() ?? new List<Tenants>();

                if (!validTenants.Any())
                {
                    Console.WriteLine("No valid tenants found for migration");
                    return;
                }

                var migrationTasks = validTenants
                    .Select(async tenant =>
                    {
                        try
                        {
                            Console.WriteLine($"Starting migration for tenant: {tenant.Id}");
                            var startTime = DateTime.UtcNow;

                            var success = await migration.ProcessLeadHistoryMigration(tenant.Id, ConnectionString);

                            var duration = DateTime.UtcNow - startTime;
                            Console.WriteLine($"Tenant {tenant.Id} migration {(success ? "completed" : "failed")} in {duration.TotalSeconds:F2} seconds");

                            return new { TenantId = tenant.Id, Success = success, Duration = duration };
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error processing tenant {tenant?.Id}: {ex.Message}");
                            return new { TenantId = tenant?.Id ?? "Unknown", Success = false, Duration = TimeSpan.Zero };
                        }
                    });

                var results = await Task.WhenAll(migrationTasks);

                // Print summary with null safety
                if (results?.Any() == true)
                {
                    var successful = results.Count(r => r?.Success == true);
                    var total = results.Length;
                    var totalTime = results.Where(r => r != null).Sum(r => r.Duration.TotalSeconds);

                    Console.WriteLine($"\nMigration Summary:");
                    Console.WriteLine($"Successful: {successful}/{total}");
                    Console.WriteLine($"Total time: {totalTime:F2} seconds");
                    Console.WriteLine($"Average time per tenant: {(total > 0 ? totalTime / total : 0):F2} seconds");
                }
                else
                {
                    Console.WriteLine("\nNo migration results to display");
                }

                // Display cache statistics
                try
                {
                    var cacheStats = CacheManager.GetCacheStats();
                    Console.WriteLine($"Cache Stats - Tenants: {cacheStats.TenantCount}, Connections: {cacheStats.ConnectionStringCount}, Lead Counts: {cacheStats.LeadCountCount}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error getting cache statistics: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
        }

        #region SQL Mapper
        private static void RegisterDapperTypeHandlers()
        {
            SqlMapper.AddTypeHandler(new DictionaryIntStringTypeHandler());
            SqlMapper.AddTypeHandler(new DictionaryIntGuidTypeHandler());
            SqlMapper.AddTypeHandler(new DictionaryIntLongTypeHandler());
            SqlMapper.AddTypeHandler(new DictionaryIntDoubleTypeHandler());
            SqlMapper.AddTypeHandler(new DictionaryIntFloatTypeHandler());
            SqlMapper.AddTypeHandler(new DictionaryIntIntTypeHandler());
            SqlMapper.AddTypeHandler(new DictionaryIntBoolTypeHandler());
            SqlMapper.AddTypeHandler(new DictionaryIntDateTimeTypeHandler());
            SqlMapper.AddTypeHandler(new DictionaryIntNullableDateTimeTypeHandler());

            // Register enum handlers
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<EnquiryType>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<SaleType>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<LeadSource>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<ContactType>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<Profession>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<UploadType>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<OfferType>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<FurnishStatus>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<LeadAssignmentType>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<BulkType>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<Purpose>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<PossesionType>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<Gender>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<MaritalStatusType>());
        }
        #endregion
    }
}
