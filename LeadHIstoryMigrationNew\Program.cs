﻿using Dapper;
using LeadHIstoryMigrationNew.Existing_Lead_Hisotry.Models;
using LeadHIstoryMigrationNew.Models;
using Npgsql;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace LeadHIstoryMigrationNew
{
    public class Program
    {
        public const string ConnectionString = "Host=lrb-qa-new.postgres.database.azure.com;Port=5432;Database=leadratBlackApp;Username=dbmasteruser;Password=********************************;Pooling=true;MinPoolSize=3;MaxPoolSize=5000;";
        
        static async Task Main(string[] args)
        {
            var conn = new NpgsqlConnection(ConnectionString);
            try
            {

                RegisterDapperTypeHandlers();

                string innerQuery = "SELECT * From \"MultiTenancy\".\"Tenants\"";
                await conn.OpenAsync();
                var tenants = (await conn.QueryAsync<Tenants>(innerQuery)).ToList();
                await conn.CloseAsync();

                // Cache tenants for potential reuse
                foreach (var tenant in tenants)
                {
                    CacheManager.GetOrAddTenant(tenant.Id, _ => tenant);
                    CacheManager.GetOrAddConnectionString(tenant.Id, _ => ConnectionString);
                }

                LeadHistoryMigration migration = new LeadHistoryMigration();

                // Process tenants in parallel for better performance
                var migrationTasks = tenants
                    .Where(tenant => tenant.Id == "ratify") // Filter as needed
                    .Select(async tenant =>
                    {
                        Console.WriteLine($"Starting migration for tenant: {tenant.Id}");
                        var startTime = DateTime.UtcNow;

                        var success = await migration.ProcessLeadHistoryMigration(tenant.Id, ConnectionString);

                        var duration = DateTime.UtcNow - startTime;
                        Console.WriteLine($"Tenant {tenant.Id} migration {(success ? "completed" : "failed")} in {duration.TotalSeconds:F2} seconds");

                        return new { TenantId = tenant.Id, Success = success, Duration = duration };
                    });

                var results = await Task.WhenAll(migrationTasks);

                // Print summary
                var successful = results.Count(r => r.Success);
                var total = results.Length;
                var totalTime = results.Sum(r => r.Duration.TotalSeconds);

                Console.WriteLine($"\nMigration Summary:");
                Console.WriteLine($"Successful: {successful}/{total}");
                Console.WriteLine($"Total time: {totalTime:F2} seconds");
                Console.WriteLine($"Average time per tenant: {(totalTime / total):F2} seconds");

                // Display cache statistics
                var cacheStats = CacheManager.GetCacheStats();
                Console.WriteLine($"Cache Stats - Tenants: {cacheStats.TenantCount}, Connections: {cacheStats.ConnectionStringCount}, Lead Counts: {cacheStats.LeadCountCount}");
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
        }

        #region SQL Mapper
        private static void RegisterDapperTypeHandlers()
        {
            SqlMapper.AddTypeHandler(new DictionaryIntStringTypeHandler());
            SqlMapper.AddTypeHandler(new DictionaryIntGuidTypeHandler());
            SqlMapper.AddTypeHandler(new DictionaryIntLongTypeHandler());
            SqlMapper.AddTypeHandler(new DictionaryIntDoubleTypeHandler());
            SqlMapper.AddTypeHandler(new DictionaryIntFloatTypeHandler());
            SqlMapper.AddTypeHandler(new DictionaryIntIntTypeHandler());
            SqlMapper.AddTypeHandler(new DictionaryIntBoolTypeHandler());
            SqlMapper.AddTypeHandler(new DictionaryIntDateTimeTypeHandler());
            SqlMapper.AddTypeHandler(new DictionaryIntNullableDateTimeTypeHandler());

            // Register enum handlers
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<EnquiryType>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<SaleType>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<LeadSource>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<ContactType>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<Profession>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<UploadType>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<OfferType>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<FurnishStatus>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<LeadAssignmentType>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<BulkType>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<Purpose>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<PossesionType>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<Gender>());
            SqlMapper.AddTypeHandler(new DictionaryIntEnumTypeHandler<MaritalStatusType>());
        }
        #endregion
    }
}
