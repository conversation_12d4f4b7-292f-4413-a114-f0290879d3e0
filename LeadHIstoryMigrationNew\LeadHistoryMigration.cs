﻿using System.Diagnostics;
using LeadHIstoryMigrationNew.Existing_Lead_Hisotry;
using LeadHIstoryMigrationNew.Existing_Lead_Hisotry.Models;
using LeadHIstoryMigrationNew.NewLeadHistory;

namespace LeadHIstoryMigrationNew
{
    public class LeadHistoryMigration
    {
        private LeadHistoryTransformer _transformer = new LeadHistoryTransformer();
        private LeadHistoryRepo _repo = new LeadHistoryRepo();
        public async Task<bool> ProcessLeadHistoryMigration(string tenantId, string connectionString)
        {
            try
            {
                var leads = await _repo.GetLeadsByTenantId(tenantId, connectionString);

                if (!leads?.Any() ?? false)
                {
                    Console.WriteLine($"No leads found for tenant {tenantId}");
                    return false;
                }

                Console.WriteLine($"Found {leads?.Count} leads for tenant {tenantId}");

                var leadIds = leads?.Select(l => l.Id).ToList();
                var existingLeadHistories = await _repo.GetLeadHistoriesByLeadIds(leadIds ?? new(), connectionString);

                if (!existingLeadHistories?.Any() ?? false)
                {
                    Console.WriteLine($"No lead histories found for tenant {tenantId}");
                    return true; // No histories to migrate
                }

                Console.WriteLine($"Found {existingLeadHistories?.Count} lead histories for tenant {tenantId}");

                // Process transformations in parallel
                var transformationTasks = existingLeadHistories?
                    .Select(history => _transformer.TransformLeadHistory(history, tenantId))
                    .ToArray();

                var transformedResults = await Task.WhenAll(transformationTasks ?? new());

                // Flatten results efficiently
                var newLeadHistory = new List<LeadHistoryHot>(transformedResults.Sum(r => r.Count));
                foreach (var transformResult in transformedResults)
                {
                    newLeadHistory.AddRange(transformResult);
                }

                Console.WriteLine($"Transformed {existingLeadHistories.Count} lead histories into {newLeadHistory.Count} field-wise records for tenant {tenantId}");

                // Migrate to database
                await _repo.MigrateNewLeadHistory(newLeadHistory, connectionString);

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in ProcessLeadHistoryMigration for tenant {tenantId}: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return false;
            }
        }

        #region Lead history transformation to field wise record
        public async Task<List<LeadHistoryHot>> TransformMultipleToFieldWiseAsync(List<LeadHistory> leadHistories, string tenantId)
        {
            if (!leadHistories?.Any() ?? false)
                return new List<LeadHistoryHot>();

            return await ProcessInParallelAsync(leadHistories, tenantId);
        }

        private async Task<List<LeadHistoryHot>> ProcessInParallelAsync(List<LeadHistory> leadHistories, string tenantId)
        {
            // Use parallel processing with optimal degree of parallelism
            var parallelOptions = new ParallelQuery<LeadHistory>(leadHistories)
                .WithDegreeOfParallelism(Environment.ProcessorCount);

            var transformationTasks = leadHistories
                .Select(history => _transformer.TransformLeadHistory(history, tenantId))
                .ToArray();

            var results = await Task.WhenAll(transformationTasks);

            // Efficiently combine results
            var totalCapacity = results.Sum(r => r.Count);
            var allRecords = new List<LeadHistoryHot>(totalCapacity);

            foreach (var result in results)
            {
                allRecords.AddRange(result);
            }

            return allRecords;
        }

        /// <summary>
        /// Legacy method - kept for compatibility but now uses the optimized transformer
        /// </summary>
        public async Task<List<LeadHistoryHot>> TransformToFieldWise(LeadHistory leadHistory, string tenantId)
        {
            if (leadHistory == null)
                throw new ArgumentNullException(nameof(leadHistory));

            return await _transformer.TransformLeadHistory(leadHistory, tenantId);
        }

        #endregion
    }
}
