﻿using System.Diagnostics;
using LeadHIstoryMigrationNew.Existing_Lead_Hisotry;
using LeadHIstoryMigrationNew.Existing_Lead_Hisotry.Models;
using LeadHIstoryMigrationNew.NewLeadHistory;

namespace LeadHIstoryMigrationNew
{
    public class LeadHistoryMigration
    {
        private LeadHistoryTransformer _transformer = new LeadHistoryTransformer();
        private LeadHistoryRepo _repo = new LeadHistoryRepo();
        public async Task<bool> ProcessLeadHistoryMigration(string tenantId, string connectionString)
        {
            // Validate input parameters
            if (string.IsNullOrWhiteSpace(tenantId))
            {
                Console.WriteLine("Error: TenantId cannot be null or empty");
                return false;
            }

            if (string.IsNullOrWhiteSpace(connectionString))
            {
                Console.WriteLine("Error: ConnectionString cannot be null or empty");
                return false;
            }

            try
            {
                // Ensure repository and transformer are initialized
                _repo ??= new LeadHistoryRepo();
                _transformer ??= new LeadHistoryTransformer();

                var leads = await _repo.GetLeadsByTenantId(tenantId, connectionString);

                if (leads == null || !leads.Any())
                {
                    Console.WriteLine($"No leads found for tenant {tenantId}");
                    return false;
                }

                Console.WriteLine($"Found {leads.Count} leads for tenant {tenantId}");

                // Extract lead IDs with null safety
                var leadIds = leads.Where(l => l?.Id != null && l.Id != Guid.Empty)
                                  .Select(l => l.Id)
                                  .ToList();

                if (!leadIds.Any())
                {
                    Console.WriteLine($"No valid lead IDs found for tenant {tenantId}");
                    return false;
                }

                var existingLeadHistories = await _repo.GetLeadHistoriesByLeadIds(leadIds, connectionString);

                if (existingLeadHistories == null || !existingLeadHistories.Any())
                {
                    Console.WriteLine($"No lead histories found for tenant {tenantId}");
                    return true; // No histories to migrate
                }

                Console.WriteLine($"Found {existingLeadHistories.Count} lead histories for tenant {tenantId}");

                // Process transformations in parallel with null safety
                var validHistories = existingLeadHistories.Where(h => h != null).ToList();

                if (!validHistories.Any())
                {
                    Console.WriteLine($"No valid lead histories found for tenant {tenantId}");
                    return true;
                }

                var transformationTasks = validHistories
                    .Select(history => SafeTransformLeadHistory(history, tenantId))
                    .ToArray();

                var transformedResults = await Task.WhenAll(transformationTasks);

                // Flatten results efficiently with null safety
                var validResults = transformedResults.Where(r => r != null && r.Any()).ToList();

                if (!validResults.Any())
                {
                    Console.WriteLine($"No valid transformation results for tenant {tenantId}");
                    return true;
                }

                var totalCapacity = validResults.Sum(r => r?.Count ?? 0);
                var newLeadHistory = new List<LeadHistoryHot>(totalCapacity);

                foreach (var transformResult in validResults)
                {
                    if (transformResult != null)
                    {
                        newLeadHistory.AddRange(transformResult);
                    }
                }

                Console.WriteLine($"Transformed {validHistories.Count} lead histories into {newLeadHistory.Count} field-wise records for tenant {tenantId}");

                // Migrate to database
                if (newLeadHistory.Any())
                {
                    await _repo.MigrateNewLeadHistory(newLeadHistory, connectionString);
                }

                return true;
            }
            catch (ArgumentNullException ex)
            {
                Console.WriteLine($"Null argument error in ProcessLeadHistoryMigration for tenant {tenantId}: {ex.Message}");
                return false;
            }
            catch (InvalidOperationException ex)
            {
                Console.WriteLine($"Invalid operation error in ProcessLeadHistoryMigration for tenant {tenantId}: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected error in ProcessLeadHistoryMigration for tenant {tenantId}: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return false;
            }
        }

        /// <summary>
        /// Safely transforms lead history with comprehensive null checking
        /// </summary>
        private async Task<List<LeadHistoryHot>> SafeTransformLeadHistory(LeadHistory leadHistory, string tenantId)
        {
            try
            {
                if (leadHistory == null)
                {
                    Console.WriteLine("Warning: Null lead history encountered, skipping transformation");
                    return new List<LeadHistoryHot>();
                }

                if (string.IsNullOrWhiteSpace(tenantId))
                {
                    Console.WriteLine("Warning: Null or empty tenant ID encountered, skipping transformation");
                    return new List<LeadHistoryHot>();
                }

                return await _transformer.TransformLeadHistory(leadHistory, tenantId);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error transforming lead history {leadHistory?.Id}: {ex.Message}");
                return new List<LeadHistoryHot>();
            }
        }

        #region Lead history transformation to field wise record
        public async Task<List<LeadHistoryHot>> TransformMultipleToFieldWiseAsync(List<LeadHistory> leadHistories, string tenantId)
        {
            if (leadHistories == null || !leadHistories.Any())
            {
                Console.WriteLine("Warning: No lead histories provided for transformation");
                return new List<LeadHistoryHot>();
            }

            if (string.IsNullOrWhiteSpace(tenantId))
            {
                Console.WriteLine("Warning: Null or empty tenant ID provided for transformation");
                return new List<LeadHistoryHot>();
            }

            return await ProcessInParallelAsync(leadHistories, tenantId);
        }

        private async Task<List<LeadHistoryHot>> ProcessInParallelAsync(List<LeadHistory> leadHistories, string tenantId)
        {
            try
            {
                if (leadHistories == null || !leadHistories.Any())
                {
                    return new List<LeadHistoryHot>();
                }

                if (string.IsNullOrWhiteSpace(tenantId))
                {
                    throw new ArgumentException("TenantId cannot be null or empty", nameof(tenantId));
                }

                // Filter out null histories before processing
                var validHistories = leadHistories.Where(h => h != null).ToList();

                if (!validHistories.Any())
                {
                    Console.WriteLine("Warning: No valid lead histories found after null filtering");
                    return new List<LeadHistoryHot>();
                }

                // Ensure transformer is initialized
                _transformer ??= new LeadHistoryTransformer();

                var transformationTasks = validHistories
                    .Select(history => SafeTransformLeadHistory(history, tenantId))
                    .ToArray();

                var results = await Task.WhenAll(transformationTasks);

                // Efficiently combine results with null safety
                var validResults = results.Where(r => r != null).ToList();
                var totalCapacity = validResults.Sum(r => r.Count);
                var allRecords = new List<LeadHistoryHot>(totalCapacity);

                foreach (var result in validResults)
                {
                    if (result != null && result.Any())
                    {
                        allRecords.AddRange(result);
                    }
                }

                return allRecords;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in ProcessInParallelAsync: {ex.Message}");
                return new List<LeadHistoryHot>();
            }
        }

        /// <summary>
        /// Legacy method - kept for compatibility but now uses the optimized transformer with null safety
        /// </summary>
        public async Task<List<LeadHistoryHot>> TransformToFieldWise(LeadHistory leadHistory, string tenantId)
        {
            if (leadHistory == null)
            {
                Console.WriteLine("Warning: Null lead history provided to TransformToFieldWise");
                return new List<LeadHistoryHot>();
            }

            if (string.IsNullOrWhiteSpace(tenantId))
            {
                Console.WriteLine("Warning: Null or empty tenant ID provided to TransformToFieldWise");
                return new List<LeadHistoryHot>();
            }

            try
            {
                // Ensure transformer is initialized
                _transformer ??= new LeadHistoryTransformer();

                return await _transformer.TransformLeadHistory(leadHistory, tenantId);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in TransformToFieldWise for lead history {leadHistory.Id}: {ex.Message}");
                return new List<LeadHistoryHot>();
            }
        }

        #endregion
    }
}
