﻿using LeadHIstoryMigrationNew.Existing_Lead_Hisotry;
using LeadHIstoryMigrationNew.Existing_Lead_Hisotry.Models;
using LeadHIstoryMigrationNew.NewLeadHistory;

namespace LeadHIstoryMigrationNew
{
    public class LeadHistoryMigration
    {
        private LeadHistoryTransformer _transformer = new LeadHistoryTransformer();
        private LeadHistoryRepo _repo = new LeadHistoryRepo();
        public async Task<bool> ProcessLeadHistoryMigration(string tenantId, string connectionString)
        {
            try
            {
                
                var leads = await _repo.GetLeadsByTenantId(tenantId, connectionString);
                if (!leads?.Any() ?? false)
                {
                    return false;
                }
                var existingLeadHistories = new List<LeadHistory>();
                foreach (var lead in leads)
                {
                    existingLeadHistories.AddRange(await _repo.GetLeadHistoryByLeadId(lead.Id, connectionString));
                }

                List<LeadHistoryHot> newLeadHistory = new List<LeadHistoryHot>();
                foreach (var history in existingLeadHistories)
                {
                    newLeadHistory.AddRange(await _transformer.TransformLeadHistory(history, tenantId));
                }

                await _repo.MigrateNewLeadHistory(newLeadHistory, connectionString);

                return true;

            }
            catch
            {
                return false;
            }
        }

        #region Lead history transformation to field wise record
        public async Task<List<LeadHistoryHot>> TransformMultipleToFieldWiseAsync(List<LeadHistory> leadHistories)
        {

            if (!leadHistories?.Any() ?? false)
                return new List<LeadHistoryHot>();

            return await ProcessInParallelAsync(leadHistories);
        }

        private async Task<List<LeadHistoryHot>> ProcessInParallelAsync(List<LeadHistory> leadHistories)
        {
            var allRecords = new List<LeadHistoryHot>();
            foreach (var history in leadHistories)
            {
                allRecords.AddRange(TransformToFieldWise(history));
            }
            return allRecords;
        }

        public List<LeadHistoryHot> TransformToFieldWise(LeadHistory leadHistory)
        {
            if (leadHistory == null)
                throw new ArgumentNullException(nameof(leadHistory));

            var fieldWiseRecords = new List<LeadHistoryHot>();

            var groupKey = Guid.NewGuid();

            return fieldWiseRecords;
        }

        #endregion
    }
}
