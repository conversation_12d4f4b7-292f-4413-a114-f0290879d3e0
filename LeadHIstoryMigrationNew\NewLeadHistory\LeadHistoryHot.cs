﻿namespace LeadHIstoryMigrationNew.NewLeadHistory
{
    #region Base Entity
    public class BaseEntity
    {
        public Guid Id { get; set; }
        public bool IsDeleted { get; set; }
    }
    #endregion

    #region Lead History Hot
    public class LeadHistoryHot : BaseEntity
    {
        public Guid LeadId { get; set; }
        public string? FieldName { get; set; }
        public string? FieldType { get; set; }
        public string? OldValue { get; set; }
        public string? NewValue { get; set; }
        public string? ModifiedBy { get; set; }
        public DateTime? ModifiedOn { get; set; }
        public Guid? LastModifiedById { get; set; }
        public Guid GroupKey { get; set; }
        public int Version { get; set; }
        public Guid? UserId { get; set; }
        public string TenantId { get; set; } = default!;
    }
    #endregion

    #region Lead History Warm
    public class LeadHistoryWarm : BaseEntity
    {
        public Guid LeadId { get; set; }
        public string? FieldName { get; set; }
        public string? FieldType { get; set; }
        public string? OldValue { get; set; }
        public string? NewValue { get; set; }
        public string? ModifiedBy { get; set; }
        public DateTime? ModifiedOn { get; set; }
        public Guid? LastModifiedById { get; set; }
        public Guid GroupKey { get; set; }
        public int Version { get; set; }
        public Guid? UserId { get; set; }
        public string TenantId { get; set; } = default!;
    }
    #endregion

    #region Lead History Cold
    public class LeadHistoryCold : BaseEntity
    {
        public Guid LeadId { get; set; }
        public string? FieldName { get; set; }
        public string? FieldType { get; set; }
        public string? OldValue { get; set; }
        public string? NewValue { get; set; }
        public string? ModifiedBy { get; set; }
        public DateTime? ModifiedOn { get; set; }
        public Guid? LastModifiedById { get; set; }
        public Guid GroupKey { get; set; }
        public int Version { get; set; }
        public Guid? UserId { get; set; }
        public string TenantId { get; set; } = default!;
    }
    #endregion
}
