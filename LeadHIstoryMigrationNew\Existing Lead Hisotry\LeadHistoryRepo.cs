﻿using Dapper;
using LeadHIstoryMigrationNew.Existing_Lead_Hisotry.Models;
using LeadHIstoryMigrationNew.NewLeadHistory;
using Npgsql;
using System.Text.Json;
using System.Data;

namespace LeadHIstoryMigrationNew.Existing_Lead_Hisotry
{
    public class LeadHistoryRepo
    {
        private const int BatchSize = 1000;
        private const int CommandTimeout = 300; // 5 minutes

        public async Task<List<Lead>> GetLeadsByTenantId(string tenantId, string connectionString)
        {
            if (string.IsNullOrWhiteSpace(tenantId))
            {
                Console.WriteLine("Error: TenantId cannot be null or empty");
                return new List<Lead>();
            }

            if (string.IsNullOrWhiteSpace(connectionString))
            {
                Console.WriteLine("Error: ConnectionString cannot be null or empty");
                return new List<Lead>();
            }

            try
            {
                using var conn = new NpgsqlConnection(connectionString);
                await conn.OpenAsync();
                var query = "SELECT \"Id\", \"Name\" FROM \"LeadratBlack\".\"Leads\" WHERE \"IsDeleted\" = false AND \"TenantId\" = @TenantId";
                var result = await conn.QueryAsync<Lead>(query, new { TenantId = tenantId }, commandTimeout: CommandTimeout);
                return result?.ToList() ?? new List<Lead>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in GetLeadsByTenantId for tenant {tenantId}: {ex.Message}");
                return new List<Lead>();
            }
        }

        public async Task<List<LeadHistory>> GetLeadHistoryByLeadId(Guid leadId, string connectionString)
        {
            if (leadId == Guid.Empty)
            {
                Console.WriteLine("Error: LeadId cannot be empty");
                return new List<LeadHistory>();
            }

            if (string.IsNullOrWhiteSpace(connectionString))
            {
                Console.WriteLine("Error: ConnectionString cannot be null or empty");
                return new List<LeadHistory>();
            }

            try
            {
                using var conn = new NpgsqlConnection(connectionString);
                await conn.OpenAsync();
                var query = "SELECT * FROM \"LeadratBlack\".\"LeadHistories\" WHERE \"IsDeleted\" = false AND \"LeadId\" = @LeadId";
                var result = await conn.QueryAsync<LeadHistory>(query, new { LeadId = leadId }, commandTimeout: CommandTimeout);
                return result?.ToList() ?? new List<LeadHistory>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in GetLeadHistoryByLeadId for lead {leadId}: {ex.Message}");
                return new List<LeadHistory>();
            }
        }

        /// <summary>
        /// Bulk retrieval of lead histories for multiple lead IDs
        /// </summary>
        public async Task<List<LeadHistory>> GetLeadHistoriesByLeadIds(IEnumerable<Guid> leadIds, string connectionString)
        {
            if (leadIds == null || !leadIds.Any())
            {
                Console.WriteLine("Warning: No lead IDs provided for bulk retrieval");
                return new List<LeadHistory>();
            }

            if (string.IsNullOrWhiteSpace(connectionString))
            {
                Console.WriteLine("Error: ConnectionString cannot be null or empty");
                return new List<LeadHistory>();
            }

            try
            {
                // Filter out empty GUIDs
                var validLeadIds = leadIds.Where(id => id != Guid.Empty).ToArray();

                if (!validLeadIds.Any())
                {
                    Console.WriteLine("Warning: No valid lead IDs found after filtering");
                    return new List<LeadHistory>();
                }

                using var conn = new NpgsqlConnection(connectionString);
                await conn.OpenAsync();
                var query = "SELECT * FROM \"LeadratBlack\".\"LeadHistories\" WHERE \"IsDeleted\" = false AND \"LeadId\" = ANY(@LeadIds)";
                var result = await conn.QueryAsync<LeadHistory>(query, new { LeadIds = validLeadIds }, commandTimeout: CommandTimeout);
                return result?.ToList() ?? new List<LeadHistory>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in GetLeadHistoriesByLeadIds: {ex.Message}");
                return new List<LeadHistory>();
            }
        }

        public async Task<bool> MigrateNewLeadHistory(List<LeadHistoryHot> leadHistories, string connectionString)
        {
            if (leadHistories == null || !leadHistories.Any())
            {
                Console.WriteLine("Warning: No lead histories provided for migration");
                return true;
            }

            if (string.IsNullOrWhiteSpace(connectionString))
            {
                Console.WriteLine("Error: ConnectionString cannot be null or empty");
                return false;
            }

            try
            {
                // Filter out null records
                var validHistories = leadHistories.Where(h => h != null).ToList();

                if (!validHistories.Any())
                {
                    Console.WriteLine("Warning: No valid lead histories found after null filtering");
                    return true;
                }

                using var conn = new NpgsqlConnection(connectionString);
                await conn.OpenAsync();
                using var transaction = await conn.BeginTransactionAsync();

                try
                {
                    var totalInserted = 0;
                    var batches = CreateBatches(validHistories, BatchSize);

                    var query = "INSERT INTO \"LeadratBlack\".\"LeadHistoryHots\"(\"Id\", \"LeadId\", \"FieldName\", \"FieldType\", \"OldValue\", \"NewValue\", \"ModifiedBy\", " +
                        "\"ModifiedOn\", \"LastModifiedById\", \"GroupKey\", \"Version\", \"UserId\", \"TenantId\", \"IsDeleted\") " +
                        "VALUES " +
                        "(@Id, @LeadId, @FieldName, @FieldType, @OldValue, @NewValue, @ModifiedBy, @ModifiedOn, @LastModifiedById, @GroupKey, @Version, @UserId, @TenantId, " +
                        "@IsDeleted)";

                    foreach (var batch in batches)
                    {
                        if (batch?.Any() == true)
                        {
                            var batchResult = await conn.ExecuteAsync(query, batch, transaction, CommandTimeout);
                            totalInserted += batchResult;
                            Console.WriteLine($"Inserted batch of {batchResult} records. Total: {totalInserted}");
                        }
                    }

                    await transaction.CommitAsync();
                    Console.WriteLine($"Successfully migrated {totalInserted} lead history records");
                    return totalInserted > 0;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error during migration transaction: {ex.Message}");
                    await transaction.RollbackAsync();
                    throw;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in MigrateNewLeadHistory: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Creates batches of the specified size from the input collection with null safety
        /// </summary>
        private static IEnumerable<IEnumerable<T>> CreateBatches<T>(IEnumerable<T> source, int batchSize)
        {
            if (source == null)
            {
                Console.WriteLine("Warning: Null source provided to CreateBatches");
                yield break;
            }

            if (batchSize <= 0)
            {
                Console.WriteLine("Warning: Invalid batch size provided to CreateBatches, using default size of 1000");
                batchSize = 1000;
            }

            var batch = new List<T>(batchSize);

            try
            {
                foreach (var item in source)
                {
                    if (item != null)
                    {
                        batch.Add(item);
                        if (batch.Count == batchSize)
                        {
                            yield return batch;
                            batch = new List<T>(batchSize);
                        }
                    }
                }

                if (batch.Count > 0)
                    yield return batch;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in CreateBatches: {ex.Message}");
                if (batch.Count > 0)
                    yield return batch;
            }
        }
    }
}
