﻿using Dapper;
using LeadHIstoryMigrationNew.Existing_Lead_Hisotry.Models;
using LeadHIstoryMigrationNew.NewLeadHistory;
using Npgsql;
using System.Text.Json;

namespace LeadHIstoryMigrationNew.Existing_Lead_Hisotry
{
    public class LeadHistoryRepo
    {
        public async Task<List<Lead>> GetLeadsByTenantId(string tenantId, string connectionString)
        {
            var conn = new NpgsqlConnection(connectionString);
            try
            {
                var query = $"SELECT \"Id\", \"Name\" FROM \"LeadratBlack\".\"Leads\" WHERE \"IsDeleted\" = false AND \"TenantId\" = '{tenantId}'";
                var result = await conn.QueryAsync<Lead>(query);
                return result.ToList();
            }
            catch
            {
                return new();
            }
        }

        public async Task<List<LeadHistory>> GetLeadHistoryByLeadId(Guid leadId, string connectionString)
        {
            var conn = new NpgsqlConnection(connectionString);
            try
            {
                var query = $"SELECT * FROM \"LeadratBlack\".\"LeadHistories\" WHERE \"IsDeleted\" = false AND \"LeadId\" = '{leadId}'";
                var result = await conn.QueryAsync<LeadHistory>(query);
                var data = JsonSerializer.Serialize(result);
                return result.ToList();
            }
            catch
            {
                return new();
            }
        }

        public async Task<bool> MigrateNewLeadHistory(List<LeadHistoryHot> leadHistories, string connectionString)
        {
            var conn = new NpgsqlConnection(connectionString);
            try
            {
                var query = $"INSERT INTO \"LeadratBlack\".\"LeadHistoryHots\"(\"Id\", \"LeadId\", \"FieldName\", \"FieldType\", \"OldValue\", \"NewValue\", \"ModifiedBy\", " +
                    $"\"ModifiedOn\", \"LastModifiedById\", \"GroupKey\", \"Version\", \"UserId\", \"TenantId\", \"IsDeleted\") " +
                    $"VALUES " +
                    $"(@Id, @LeadId, @FieldName, @FieldType, @OldValue, @NewValue, @ModifiedBy, @ModifiedOn, @LastModifiedById, @GroupKey, @Version, @UserId, @TenantId, " +
                    $"@IsDeleted)";
                var res = await conn.ExecuteAsync(query, leadHistories);
                Console.WriteLine(res);
                if(res > 0)
                {
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }
    }
}
