﻿using Dapper;
using LeadHIstoryMigrationNew.Existing_Lead_Hisotry.Models;
using LeadHIstoryMigrationNew.NewLeadHistory;
using Npgsql;
using System.Text.Json;
using System.Data;

namespace LeadHIstoryMigrationNew.Existing_Lead_Hisotry
{
    public class LeadHistoryRepo
    {
        private const int BatchSize = 1000;
        private const int CommandTimeout = 300; // 5 minutes

        public async Task<List<Lead>> GetLeadsByTenantId(string tenantId, string connectionString)
        {
            using var conn = new NpgsqlConnection(connectionString);
            try
            {
                await conn.OpenAsync();
                var query = "SELECT \"Id\", \"Name\" FROM \"LeadratBlack\".\"Leads\" WHERE \"IsDeleted\" = false AND \"TenantId\" = @TenantId";
                var result = await conn.QueryAsync<Lead>(query, new { TenantId = tenantId }, commandTimeout: CommandTimeout);
                return result.ToList();
            }
            catch
            {
                return new();
            }
        }

        public async Task<List<LeadHistory>> GetLeadHistoryByLeadId(Guid leadId, string connectionString)
        {
            using var conn = new NpgsqlConnection(connectionString);
            try
            {
                await conn.OpenAsync();
                var query = "SELECT * FROM \"LeadratBlack\".\"LeadHistories\" WHERE \"IsDeleted\" = false AND \"LeadId\" = @LeadId";
                var result = await conn.QueryAsync<LeadHistory>(query, new { LeadId = leadId }, commandTimeout: CommandTimeout);
                return result.ToList();
            }
            catch
            {
                return new();
            }
        }

        /// <summary>
        /// Bulk retrieval of lead histories for multiple lead IDs
        /// </summary>
        public async Task<List<LeadHistory>> GetLeadHistoriesByLeadIds(IEnumerable<Guid> leadIds, string connectionString)
        {
            if (!leadIds?.Any() ?? true)
                return new List<LeadHistory>();

            using var conn = new NpgsqlConnection(connectionString);
            try
            {
                await conn.OpenAsync();
                var query = "SELECT * FROM \"LeadratBlack\".\"LeadHistories\" WHERE \"IsDeleted\" = false AND \"LeadId\" = ANY(@LeadIds)";
                var result = await conn.QueryAsync<LeadHistory>(query, new { LeadIds = leadIds.ToArray() }, commandTimeout: CommandTimeout);
                return result.ToList();
            }
            catch
            {
                return new();
            }
        }

        public async Task<bool> MigrateNewLeadHistory(List<LeadHistoryHot> leadHistories, string connectionString)
        {
            if (!leadHistories?.Any() ?? true)
                return true;

            using var conn = new NpgsqlConnection(connectionString);
            try
            {
                await conn.OpenAsync();
                using var transaction = await conn.BeginTransactionAsync();

                try
                {
                    var totalInserted = 0;
                    var batches = CreateBatches(leadHistories, BatchSize);

                    var query = "INSERT INTO \"LeadratBlack\".\"LeadHistoryHots\"(\"Id\", \"LeadId\", \"FieldName\", \"FieldType\", \"OldValue\", \"NewValue\", \"ModifiedBy\", " +
                        "\"ModifiedOn\", \"LastModifiedById\", \"GroupKey\", \"Version\", \"UserId\", \"TenantId\", \"IsDeleted\") " +
                        "VALUES " +
                        "(@Id, @LeadId, @FieldName, @FieldType, @OldValue, @NewValue, @ModifiedBy, @ModifiedOn, @LastModifiedById, @GroupKey, @Version, @UserId, @TenantId, " +
                        "@IsDeleted)";

                    foreach (var batch in batches)
                    {
                        var batchResult = await conn.ExecuteAsync(query, batch, transaction, CommandTimeout);
                        totalInserted += batchResult;
                        Console.WriteLine($"Inserted batch of {batchResult} records. Total: {totalInserted}");
                    }

                    await transaction.CommitAsync();
                    Console.WriteLine($"Successfully migrated {totalInserted} lead history records");
                    return totalInserted > 0;
                }
                catch
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Creates batches of the specified size from the input collection
        /// </summary>
        private static IEnumerable<IEnumerable<T>> CreateBatches<T>(IEnumerable<T> source, int batchSize)
        {
            var batch = new List<T>(batchSize);
            foreach (var item in source)
            {
                batch.Add(item);
                if (batch.Count == batchSize)
                {
                    yield return batch;
                    batch = new List<T>(batchSize);
                }
            }

            if (batch.Count > 0)
                yield return batch;
        }
    }
}
