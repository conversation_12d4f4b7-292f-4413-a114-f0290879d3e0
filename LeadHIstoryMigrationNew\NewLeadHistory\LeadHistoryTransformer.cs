using LeadHIstoryMigrationNew.Existing_Lead_Hisotry.Models;
using Npgsql;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Reflection;
using System.Collections.Concurrent;

namespace LeadHIstoryMigrationNew.NewLeadHistory
{
    public class LeadHistoryTransformer
    {
        // Cache for reflection operations to avoid repeated reflection calls
        private static readonly ConcurrentDictionary<Type, PropertyInfo[]> _propertyCache = new();
        private static readonly ConcurrentDictionary<Type, string> _fieldTypeCache = new();
        private static readonly PropertyInfo[] _leadHistoryProperties = GetCachedProperties(typeof(LeadHistory));

        // Pre-compiled property accessors for better performance
        private static readonly PropertyInfo _keysProperty = typeof(IDictionary<int, object>).GetProperty("Keys");
        private static readonly PropertyInfo _itemProperty = typeof(IDictionary<int, object>).GetProperty("Item");
        public async Task<List<LeadHistoryHot>> TransformLeadHistory(LeadHistory leadHistory, string tenantId)
        {
            // Pre-allocate with estimated capacity to reduce memory allocations
            var result = new List<LeadHistoryHot>(capacity: 100);

            // Use cached properties instead of reflection each time
            var properties = _leadHistoryProperties;

            foreach (var property in properties)
            {
                var fieldName = property.Name;
                var dictionary = property.GetValue(leadHistory);

                if (dictionary == null)
                    continue;

                var dictType = dictionary.GetType();
                var keysProperty = dictType.GetProperty("Keys");
                var itemProperty = dictType.GetProperty("Item");
                var keys = (IEnumerable<int>)keysProperty.GetValue(dictionary);

                // Convert to array and sort in-place for better performance
                var sortedKeys = keys.ToArray();
                Array.Sort(sortedKeys);

                for (int i = 0; i < sortedKeys.Length; i++)
                {
                    var version = sortedKeys[i];
                    var currentValue = itemProperty.GetValue(dictionary, new object[] { version });

                    // Convert to string early to check for filtering
                    var newValueString = ConvertToString(currentValue);

                    // Skip fields with null, empty, or whitespace-only values
                    if (IsValueEmpty(newValueString))
                        continue;

                    string oldValue = null;
                    if (i > 0)
                    {
                        var previousVersion = sortedKeys[i - 1];
                        var previousValue = itemProperty.GetValue(dictionary, new object[] { previousVersion });
                        oldValue = ConvertToString(previousValue);
                    }

                    var fieldType = GetCachedFieldType(property.PropertyType);

                    DateTime? modifiedOn = null;
                    string modifiedBy = null;
                    Guid? lastModifiedById = null;

                    if (leadHistory.ModifiedDate != null && leadHistory.ModifiedDate.ContainsKey(version))
                    {
                        if (DateTime.TryParse(leadHistory.ModifiedDate[version], out DateTime parsedDate))
                            modifiedOn = parsedDate;
                    }

                    if (leadHistory.LastModifiedBy != null && leadHistory.LastModifiedBy.ContainsKey(version))
                    {
                        modifiedBy = leadHistory.LastModifiedBy[version];
                    }

                    if (leadHistory.LastModifiedByUser != null && leadHistory.LastModifiedByUser.ContainsKey(version))
                    {
                        if (Guid.TryParse(leadHistory.LastModifiedByUser[version].ToString(), out Guid userId))
                            lastModifiedById = userId;
                    }

                    result.Add(new LeadHistoryHot
                    {
                        Id = Guid.NewGuid(),
                        LeadId = leadHistory.LeadId,
                        FieldName = fieldName,
                        FieldType = fieldType,
                        OldValue = oldValue,
                        NewValue = newValueString, // Use the already converted string
                        ModifiedBy = modifiedBy,
                        ModifiedOn = modifiedOn,
                        LastModifiedById = lastModifiedById,
                        GroupKey = leadHistory.Id, // Using the original LeadHistory Id as GroupKey
                        Version = version,
                        UserId = leadHistory.UserId,
                        TenantId = tenantId,
                        IsDeleted = false
                    });
                }
            }

            return result.OrderBy(r => r.Version).ThenBy(r => r.FieldName).ToList();
        }

        /// <summary>
        /// Checks if a value is null, empty, or contains only whitespace
        /// </summary>
        /// <param name="value">The value to check</param>
        /// <returns>True if the value should be filtered out</returns>
        private static bool IsValueEmpty(string value)
        {
            return value == null ||
                   value.Length == 0 ||
                   string.IsNullOrWhiteSpace(value);
        }

        /// <summary>
        /// Gets cached properties for a type to avoid repeated reflection
        /// </summary>
        private static PropertyInfo[] GetCachedProperties(Type type)
        {
            return _propertyCache.GetOrAdd(type, t =>
                t.GetProperties()
                    .Where(p => p.PropertyType.IsGenericType &&
                               p.PropertyType.GetGenericTypeDefinition() == typeof(IDictionary<,>) &&
                               p.PropertyType.GetGenericArguments()[0] == typeof(int))
                    .ToArray());
        }

        /// <summary>
        /// Gets cached field type to avoid repeated reflection and string operations
        /// </summary>
        private static string GetCachedFieldType(Type propertyType)
        {
            return _fieldTypeCache.GetOrAdd(propertyType, GetFieldType);
        }

        private string ConvertToString(object value)
        {
            if (value == null)
                return null;

            // Handle different types
            if (value is DateTime dateTime)
                return dateTime.ToString("O"); // ISO 8601 format

            if (value is Guid guid)
                return guid.ToString();

            if (value is bool boolValue)
                return boolValue.ToString();

            if (value is Enum enumValue)
                return enumValue.ToString();

            return value.ToString();
        }

        private string GetFieldType(Type propertyType)
        {
            if (!propertyType.IsGenericType)
                return "Unknown";

            var valueType = propertyType.GetGenericArguments()[1];

            if (valueType == typeof(string))
                return "String";
            if (valueType == typeof(Guid))
                return "Guid";
            if (valueType == typeof(int))
                return "Int32";
            if (valueType == typeof(long))
                return "Int64";
            if (valueType == typeof(double))
                return "Double";
            if (valueType == typeof(float))
                return "Single";
            if (valueType == typeof(bool))
                return "Boolean";
            if (valueType == typeof(DateTime))
                return "DateTime";
            if (valueType == typeof(DateTime?))
                return "Nullable`1[DateTime]";
            if (valueType.IsEnum)
                return valueType.Name;

            return valueType.Name;
        }
    }
}
