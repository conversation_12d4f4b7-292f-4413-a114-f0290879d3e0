using LeadHIstoryMigrationNew.Existing_Lead_Hisotry.Models;
using Npgsql;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace LeadHIstoryMigrationNew.NewLeadHistory
{
    public class LeadHistoryTransformer
    {
        public async Task<List<LeadHistoryHot>> TransformLeadHistory(LeadHistory leadHistory, string tenantId)
        {
            var result = new List<LeadHistoryHot>();

            var properties = typeof(LeadHistory).GetProperties()
                .Where(p => p.PropertyType.IsGenericType &&
                           p.PropertyType.GetGenericTypeDefinition() == typeof(IDictionary<,>) &&
                           p.PropertyType.GetGenericArguments()[0] == typeof(int));

            foreach (var property in properties)
            {
                var fieldName = property.Name;
                var dictionary = property.GetValue(leadHistory);

                if (dictionary == null)
                    continue;

                var dictType = dictionary.GetType();
                var keys = (IEnumerable<int>)dictType.GetProperty("Keys").GetValue(dictionary);
                var sortedKeys = keys.OrderBy(k => k).ToList();

                for (int i = 0; i < sortedKeys.Count; i++)
                {
                    var version = sortedKeys[i];
                    var currentValue = dictType.GetProperty("Item").GetValue(dictionary, new object[] { version });

                    string oldValue = null;
                    if (i > 0)
                    {
                        var previousVersion = sortedKeys[i - 1];
                        var previousValue = dictType.GetProperty("Item").GetValue(dictionary, new object[] { previousVersion });
                        oldValue = ConvertToString(previousValue);
                    }

                    var fieldType = GetFieldType(property.PropertyType);

                    DateTime? modifiedOn = null;
                    string modifiedBy = null;
                    Guid? lastModifiedById = null;

                    if (leadHistory.ModifiedDate != null && leadHistory.ModifiedDate.ContainsKey(version))
                    {
                        if (DateTime.TryParse(leadHistory.ModifiedDate[version], out DateTime parsedDate))
                            modifiedOn = parsedDate;
                    }

                    if (leadHistory.LastModifiedBy != null && leadHistory.LastModifiedBy.ContainsKey(version))
                    {
                        modifiedBy = leadHistory.LastModifiedBy[version];
                    }

                    if (leadHistory.LastModifiedByUser != null && leadHistory.LastModifiedByUser.ContainsKey(version))
                    {
                        if (Guid.TryParse(leadHistory.LastModifiedByUser[version].ToString(), out Guid userId))
                            lastModifiedById = userId;
                    }

                    result.Add(new LeadHistoryHot
                    {
                        Id = Guid.NewGuid(),
                        LeadId = leadHistory.LeadId,
                        FieldName = fieldName,
                        FieldType = fieldType,
                        OldValue = oldValue,
                        NewValue = ConvertToString(currentValue),
                        ModifiedBy = modifiedBy,
                        ModifiedOn = modifiedOn,
                        LastModifiedById = lastModifiedById,
                        GroupKey = leadHistory.Id, // Using the original LeadHistory Id as GroupKey
                        Version = version,
                        UserId = leadHistory.UserId,
                        TenantId = tenantId,
                        IsDeleted = false
                    });
                }
            }

            return result.OrderBy(r => r.Version).ThenBy(r => r.FieldName).ToList();
        }

        private string ConvertToString(object value)
        {
            if (value == null)
                return null;

            // Handle different types
            if (value is DateTime dateTime)
                return dateTime.ToString("O"); // ISO 8601 format

            if (value is Guid guid)
                return guid.ToString();

            if (value is bool boolValue)
                return boolValue.ToString();

            if (value is Enum enumValue)
                return enumValue.ToString();

            return value.ToString();
        }

        private string GetFieldType(Type propertyType)
        {
            if (!propertyType.IsGenericType)
                return "Unknown";

            var valueType = propertyType.GetGenericArguments()[1];

            if (valueType == typeof(string))
                return "String";
            if (valueType == typeof(Guid))
                return "Guid";
            if (valueType == typeof(int))
                return "Int32";
            if (valueType == typeof(long))
                return "Int64";
            if (valueType == typeof(double))
                return "Double";
            if (valueType == typeof(float))
                return "Single";
            if (valueType == typeof(bool))
                return "Boolean";
            if (valueType == typeof(DateTime))
                return "DateTime";
            if (valueType == typeof(DateTime?))
                return "Nullable`1[DateTime]";
            if (valueType.IsEnum)
                return valueType.Name;

            return valueType.Name;
        }
    }
}
