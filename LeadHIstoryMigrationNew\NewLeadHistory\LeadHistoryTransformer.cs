using LeadHIstoryMigrationNew.Existing_Lead_Hisotry.Models;
using Npgsql;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Reflection;
using System.Collections.Concurrent;

namespace LeadHIstoryMigrationNew.NewLeadHistory
{
    public class LeadHistoryTransformer
    {
        // Cache for reflection operations to avoid repeated reflection calls
        private static readonly ConcurrentDictionary<Type, PropertyInfo[]> _propertyCache = new();
        private static readonly ConcurrentDictionary<Type, string> _fieldTypeCache = new();
        private static readonly PropertyInfo[] _leadHistoryProperties = GetCachedProperties(typeof(LeadHistory));

        // Pre-compiled property accessors for better performance
        private static readonly PropertyInfo _keysProperty = typeof(IDictionary<int, object>).GetProperty("Keys");
        private static readonly PropertyInfo _itemProperty = typeof(IDictionary<int, object>).GetProperty("Item");
        public async Task<List<LeadHistoryHot>> TransformLeadHistory(LeadHistory leadHistory, string tenantId)
        {
            // Validate input parameters
            if (leadHistory == null)
            {
                throw new ArgumentNullException(nameof(leadHistory), "LeadHistory cannot be null");
            }

            if (string.IsNullOrWhiteSpace(tenantId))
            {
                throw new ArgumentException("TenantId cannot be null or empty", nameof(tenantId));
            }

            try
            {
                // Pre-allocate with estimated capacity to reduce memory allocations
                var result = new List<LeadHistoryHot>(capacity: 100);

                // Use cached properties instead of reflection each time
                var properties = _leadHistoryProperties;

                if (properties == null || properties.Length == 0)
                {
                    Console.WriteLine("Warning: No properties found for LeadHistory type");
                    return result;
                }

                foreach (var property in properties)
                {
                    try
                    {
                        if (property == null)
                        {
                            Console.WriteLine("Warning: Null property encountered, skipping");
                            continue;
                        }

                        var fieldName = property.Name;
                        if (string.IsNullOrWhiteSpace(fieldName))
                        {
                            Console.WriteLine("Warning: Property with null or empty name encountered, skipping");
                            continue;
                        }

                        var dictionary = property.GetValue(leadHistory);

                        if (dictionary == null)
                            continue;

                        var dictType = dictionary.GetType();
                        var keysProperty = dictType.GetProperty("Keys");
                        var itemProperty = dictType.GetProperty("Item");

                        if (keysProperty == null || itemProperty == null)
                        {
                            Console.WriteLine($"Warning: Dictionary properties not found for field {fieldName}, skipping");
                            continue;
                        }

                        var keysObject = keysProperty.GetValue(dictionary);
                        if (keysObject == null)
                        {
                            Console.WriteLine($"Warning: Keys collection is null for field {fieldName}, skipping");
                            continue;
                        }

                        var keys = (IEnumerable<int>)keysObject;
                        if (keys == null)
                        {
                            Console.WriteLine($"Warning: Keys enumerable is null for field {fieldName}, skipping");
                            continue;
                        }

                        // Convert to array and sort in-place for better performance
                        var sortedKeys = keys.ToArray();
                        if (sortedKeys.Length == 0)
                        {
                            continue; // No versions to process
                        }

                        Array.Sort(sortedKeys);

                        for (int i = 0; i < sortedKeys.Length; i++)
                        {
                            try
                            {
                                var version = sortedKeys[i];
                                var currentValue = itemProperty.GetValue(dictionary, new object[] { version });

                                // Convert to string early to check for filtering
                                var newValueString = ConvertToString(currentValue);

                                // Skip fields with null, empty, or whitespace-only values
                                if (IsValueEmpty(newValueString))
                                    continue;

                                string oldValue = null;
                                if (i > 0)
                                {
                                    var previousVersion = sortedKeys[i - 1];
                                    var previousValue = itemProperty.GetValue(dictionary, new object[] { previousVersion });
                                    oldValue = ConvertToString(previousValue);
                                }

                                var fieldType = GetCachedFieldType(property.PropertyType);

                                DateTime? modifiedOn = null;
                                string modifiedBy = null;
                                Guid? lastModifiedById = null;

                                // Safely extract modification metadata
                                try
                                {
                                    if (leadHistory.ModifiedDate != null && leadHistory.ModifiedDate.ContainsKey(version))
                                    {
                                        var modifiedDateValue = leadHistory.ModifiedDate[version];
                                        if (!string.IsNullOrWhiteSpace(modifiedDateValue) &&
                                            DateTime.TryParse(modifiedDateValue, out DateTime parsedDate))
                                        {
                                            modifiedOn = parsedDate;
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"Warning: Error parsing ModifiedDate for field {fieldName}, version {version}: {ex.Message}");
                                }

                                try
                                {
                                    if (leadHistory.LastModifiedBy != null && leadHistory.LastModifiedBy.ContainsKey(version))
                                    {
                                        modifiedBy = leadHistory.LastModifiedBy[version];
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"Warning: Error getting LastModifiedBy for field {fieldName}, version {version}: {ex.Message}");
                                }

                                try
                                {
                                    if (leadHistory.LastModifiedByUser != null && leadHistory.LastModifiedByUser.ContainsKey(version))
                                    {
                                        var userIdValue = leadHistory.LastModifiedByUser[version];
                                        if (userIdValue != null &&
                                            Guid.TryParse(userIdValue.ToString(), out Guid userId))
                                        {
                                            lastModifiedById = userId;
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"Warning: Error parsing LastModifiedByUser for field {fieldName}, version {version}: {ex.Message}");
                                }

                                // Create the lead history record
                                try
                                {
                                    result.Add(new LeadHistoryHot
                                    {
                                        Id = Guid.NewGuid(),
                                        LeadId = leadHistory.LeadId,
                                        FieldName = fieldName,
                                        FieldType = fieldType ?? "Unknown",
                                        OldValue = oldValue,
                                        NewValue = newValueString,
                                        ModifiedBy = modifiedBy,
                                        ModifiedOn = modifiedOn,
                                        LastModifiedById = lastModifiedById,
                                        GroupKey = leadHistory.Id,
                                        Version = version,
                                        UserId = leadHistory.UserId,
                                        TenantId = tenantId,
                                        IsDeleted = false
                                    });
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"Warning: Error creating LeadHistoryHot record for field {fieldName}, version {version}: {ex.Message}");
                                }
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"Warning: Error processing version {version} for field {fieldName}: {ex.Message}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Warning: Error processing property {fieldName}: {ex.Message}");
                    }
                }

                return result.OrderBy(r => r.Version).ThenBy(r => r.FieldName).ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in TransformLeadHistory: {ex.Message}");
                throw; // Re-throw to maintain the contract
            }
        }

        /// <summary>
        /// Checks if a value is null, empty, or contains only whitespace
        /// </summary>
        /// <param name="value">The value to check</param>
        /// <returns>True if the value should be filtered out</returns>
        private static bool IsValueEmpty(string value)
        {
            return value == null ||
                   value.Length == 0 ||
                   string.IsNullOrWhiteSpace(value);
        }

        /// <summary>
        /// Gets cached properties for a type to avoid repeated reflection
        /// </summary>
        private static PropertyInfo[] GetCachedProperties(Type type)
        {
            if (type == null)
            {
                Console.WriteLine("Warning: Null type provided to GetCachedProperties");
                return Array.Empty<PropertyInfo>();
            }

            try
            {
                return _propertyCache.GetOrAdd(type, t =>
                {
                    try
                    {
                        return t.GetProperties()
                            .Where(p => p?.PropertyType?.IsGenericType == true &&
                                       p.PropertyType.GetGenericTypeDefinition() == typeof(IDictionary<,>) &&
                                       p.PropertyType.GetGenericArguments()?.Length > 0 &&
                                       p.PropertyType.GetGenericArguments()[0] == typeof(int))
                            .ToArray();
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Warning: Error getting properties for type {t?.Name}: {ex.Message}");
                        return Array.Empty<PropertyInfo>();
                    }
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Warning: Error in GetCachedProperties for type {type?.Name}: {ex.Message}");
                return Array.Empty<PropertyInfo>();
            }
        }

        /// <summary>
        /// Gets cached field type to avoid repeated reflection and string operations
        /// </summary>
        private static string GetCachedFieldType(Type propertyType)
        {
            if (propertyType == null)
            {
                Console.WriteLine("Warning: Null property type provided to GetCachedFieldType");
                return "Unknown";
            }

            try
            {
                return _fieldTypeCache.GetOrAdd(propertyType, GetFieldType);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Warning: Error getting cached field type for {propertyType?.Name}: {ex.Message}");
                return "Unknown";
            }
        }

        private string ConvertToString(object value)
        {
            if (value == null)
                return null;

            try
            {
                // Handle different types
                if (value is DateTime dateTime)
                    return dateTime.ToString("O"); // ISO 8601 format

                if (value is Guid guid)
                    return guid.ToString();

                if (value is bool boolValue)
                    return boolValue.ToString();

                if (value is Enum enumValue)
                    return enumValue.ToString();

                return value.ToString();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Warning: Error converting value to string: {ex.Message}");
                return value?.ToString() ?? string.Empty;
            }
        }

        private string GetFieldType(Type propertyType)
        {
            if (!propertyType.IsGenericType)
                return "Unknown";

            var valueType = propertyType.GetGenericArguments()[1];

            if (valueType == typeof(string))
                return "String";
            if (valueType == typeof(Guid))
                return "Guid";
            if (valueType == typeof(int))
                return "Int32";
            if (valueType == typeof(long))
                return "Int64";
            if (valueType == typeof(double))
                return "Double";
            if (valueType == typeof(float))
                return "Single";
            if (valueType == typeof(bool))
                return "Boolean";
            if (valueType == typeof(DateTime))
                return "DateTime";
            if (valueType == typeof(DateTime?))
                return "Nullable`1[DateTime]";
            if (valueType.IsEnum)
                return valueType.Name;

            return valueType.Name;
        }
    }
}
