﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations.Schema;

namespace LeadHIstoryMigrationNew.Existing_Lead_Hisotry.Models
{
    public class LeadHistory
    {
        // Base Entity
        public Guid Id { get; set; }
        public bool IsDeleted { get; set; }

        // Lead History Specific
        #region AuditableBase
        public DateTime CreatedDate { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? ModifiedDate { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, Guid>? AssignedTo { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? AssignmentType { get; set; }
        public Guid CreatedBy { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? LastModifiedBy { get; set; }
        #endregion

        [Column(TypeName = "jsonb")]
        public IDictionary<int, EnquiryType>? EnquiredFor { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, SaleType>? SaleType { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? BasePropertyType { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? SubPropertyType { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? BHKType { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? NoOfBHK { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? Name { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? Email { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? ContactNo { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? AlternateContactNo { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, long>? LowerBudget { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, long>? UpperBudget { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, double>? Area { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? AreaUnit { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? Notes { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? ConfidentialNotes { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? EnquiredCity { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? EnquiredState { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? EnquiredLocation { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, LeadSource>? LeadSource { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? Rating { get; set; } // 1,2,3,4,5 stars
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? BaseLeadStatus { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? SubLeadStatus { get; set; }
        //LeadSubStatus
        [Column(TypeName = "jsonb")]
        public IDictionary<int, DateTime?>? ScheduledDate { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? LeadNumber { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? ChosenProject { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? ChosenProperty { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? BookedUnderName { get; set; }

        [Column(TypeName = "jsonb")]
        public IDictionary<int, DateTime?>? RevertDate { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? SoldPrice { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, bool>? IsHighlighted { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, bool>? IsEscalated { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, bool>? IsAboutToConvert { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, bool>? IsIntegrationLead { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, int>? ShareCount { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, bool>? IsHotLead { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? AssignedToUser { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? AssignedFromUser { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, bool>? IsColdLead { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, bool>? IsWarmLead { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, ContactType>? ContactRecords { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? Documents { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, Guid>? LastModifiedByUser { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, bool>? IsMeetingDone { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, Guid>? MeetingLocation { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, bool>? IsSiteVisitDone { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, Guid>? SiteLocation { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, bool>? IsArchived { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, Guid>? ArchivedBy { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, DateTime>? ArchivedDate { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, Guid>? RestoredBy { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, DateTime>? RestoredDate { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? Projects { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? Properties { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? SubSource { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? ReferralName { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? ReferralContactNo { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? AgencyName { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? CompanyName { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, DateTime?>? PossessionDate { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, double>? CarpetArea { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? CarpetAreaUnit { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, float>? ConversionFactor { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, int>? ChildLeadsCount { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, Guid>? SourcingManager { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, Guid>? ClosingManager { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, Profession>? Profession { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? CustomerCity { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? CustomerState { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? CustomerLocation { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? ChannelPartnerName { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? ChannelPartnerExecutiveName { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? ChannelPartnerContactNo { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? SourcingManagerUser { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? ClosingManagerUser { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? ChannelPartners { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, Guid>? SecondaryUserId { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? SecondaryUser { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, DateTime?>? PickedDate { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, bool>? IsPicked { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, DateTime?>? BookedDate { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, Guid>? BookedBy { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? BookedByUser { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? CustomFlags { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, UploadType>? UploadType { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? UploadTypeName { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? EnquiryTypes { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? BHKTypes { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? BHKs { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? Beds { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? Baths { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? Floors { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, OfferType>? OfferType { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, FurnishStatus>? Furnished { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? EnquiredCities { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? EnquiredCommunity { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? EnquiredSubCommunity { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? EnquiredTowerName { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? EnquiredStates { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? EnquiredLocations { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? Agencies { get; set; }
        public string? DuplicateLeadVersion { get; set; }
        public Guid UserId { get; set; }
        public Guid LeadId { get; set; }
        // Version index
        public int CurrentVersion { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, LeadAssignmentType>? LeadAssignmentType { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? Designation { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, BulkType>? BulkCategory { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? SecondaryFromUser { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? Links { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, double>? BuiltUpArea { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? BuiltUpAreaUnit { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, double>? SaleableArea { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? SaleableAreaUnit { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, float>? BuiltUpAreaConversionFactor { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, float>? SaleableAreaConversionFactor { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? ReferralEmail { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? EnquiredCountry { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? CustomerCommunity { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? CustomerSubCommunity { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? CustomerTowerName { get; set; }

        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? CustomerCountry { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? Currency { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, double>? PropertyArea { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? PropertyAreaUnit { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, float>? PropertyAreaConversionFactor { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, double>? NetArea { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? NetAreaUnit { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, float>? NetAreaConversionFactor { get; set; }

        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? UnitName { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? ClusterName { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? Nationality { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? Campaigns { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, Purpose>? Purpose { get; set; }

        [Column(TypeName = "jsonb")]
        public IDictionary<int, PossesionType>? PossesionType { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? LandLine { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, Gender>? Gender { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, MaritalStatusType>? MaritalStatus { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, DateTime?>? DateOfBirth { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, DateTime?>? AppointmentDoneOn { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, DateTime?>? AnniversaryDate { get; set; }
    }

    #region Enquiry Type
    public enum EnquiryType
    {
        [Description("None")]
        None = 0,
        [Description("Buy")]
        Buy,
        [Description("Sale")]
        Sale,
        [Description("Rent")]
        Rent
    }
    #endregion

    #region Sale Type
    public enum SaleType
    {
        [Description("None")]
        None = 0,
        [Description("New")]
        New,
        [Description("Resale")]
        Resale
    }
    #endregion

    #region Lead Source
    public enum LeadSource
    {
        Any = -1,
        [Description(EnumDescription.LeadSource.Direct)]
        Direct = 0,
        [Description(EnumDescription.LeadSource.IVR)]
        IVR,
        [Description(EnumDescription.LeadSource.Facebook)]
        Facebook,
        [Description(EnumDescription.LeadSource.LinkedIn)]
        LinkedIn,
        [Description(EnumDescription.LeadSource.GoogleAds)]
        GoogleAds,
        [Description(EnumDescription.LeadSource.MagicBricks)]
        MagicBricks,
        [Description(EnumDescription.LeadSource.NinetyNineAcres)]
        NinetyNineAcres,
        [Description(EnumDescription.LeadSource.Housing)]
        Housing,
        [Description(EnumDescription.LeadSource.GharOffice)]
        GharOffice,
        [Description(EnumDescription.LeadSource.Referral)]
        Referral,
        [Description(EnumDescription.LeadSource.WalkIn)]
        WalkIn,
        [Description(EnumDescription.LeadSource.Website)]
        Website,
        [Description(EnumDescription.LeadSource.Gmail)]
        Gmail,
        [Description(EnumDescription.LeadSource.PropertyMicrosite)]
        PropertyMicrosite,
        [Description(EnumDescription.LeadSource.PortfolioMicrosite)]
        PortfolioMicrosite,
        [Description(EnumDescription.LeadSource.Phonebook)]
        Phonebook,
        [Description(EnumDescription.LeadSource.CallLogs)]
        CallLogs,
        [Description(EnumDescription.LeadSource.LeadPool)]
        LeadPool,
        [Description(EnumDescription.LeadSource.SquareYards)]
        SquareYards,
        [Description(EnumDescription.LeadSource.QuikrHomes)]
        QuikrHomes,
        [Description(EnumDescription.LeadSource.JustLead)]
        JustLead,
        [Description(EnumDescription.LeadSource.WhatsApp)]
        WhatsApp,
        [Description(EnumDescription.LeadSource.YouTube)]
        YouTube,
        [Description(EnumDescription.LeadSource.QRCode)]
        QRCode,
        [Description(EnumDescription.LeadSource.Instagram)]
        Instagram,
        [Description(EnumDescription.LeadSource.OLX)]
        OLX,
        [Description(EnumDescription.LeadSource.EstateDekho)]
        EstateDekho,
        [Description(EnumDescription.LeadSource.GoogleSheet)]
        GoogleSheet,
        [Description(EnumDescription.LeadSource.ChannelPartner)]
        ChannelPartner,
        [Description(EnumDescription.LeadSource.RealEstateIndia)]
        RealEstateIndia,
        [Description(EnumDescription.LeadSource.CommonFloor)]
        CommonFloor,
        [Description(EnumDescription.LeadSource.Data)]
        Data,
        [Description(EnumDescription.LeadSource.RoofandFloor)]
        RoofandFloor,
        [Description(EnumDescription.LeadSource.MicrosoftAds)]
        MicrosoftAds,
        [Description(EnumDescription.LeadSource.PropertyWala)]
        PropertyWala,
        [Description(EnumDescription.LeadSource.ProjectMicrosite)]
        ProjectMicrosite,
        [Description(EnumDescription.LeadSource.MyGate)]
        MyGate,
        [Description(EnumDescription.LeadSource.Flipkart)]
        Flipkart,
        [Description(EnumDescription.LeadSource.PropertyFinder)]
        PropertyFinder,
        [Description(EnumDescription.LeadSource.Bayut)]
        Bayut,
        [Description(EnumDescription.LeadSource.Dubizzle)]
        Dubizzle,
        [Description(EnumDescription.LeadSource.Webhook)]
        Webhook,
        [Description(EnumDescription.LeadSource.TikTok)]
        TikTok,
        [Description(EnumDescription.LeadSource.Snapchat)]
        Snapchat,
        [Description(EnumDescription.LeadSource.GoogleAdsCampaign)]
        GoogleAdsCampaign
    }
    #endregion

    #region ContactType
    public enum ContactType
    {
        [Description("WhatsApp")]
        WhatsApp = 0,
        [Description("Call")]
        Call,
        [Description("Email")]
        Email,
        [Description("SMS")]
        SMS,
        [Description("Push Notification")]
        PushNotification
    }
    #endregion

    #region Profession
    public enum Profession
    {
        None = 0,
        Salaried,
        Business,
        SelfEmployed,
        Doctor,
        Retired,
        Housewife,
        Student,
        Unemployed,
        Others
    }
    #endregion

    #region Upload Type
    public enum UploadType
    {
        None = 0,
        [Description("Excel Upload")]
        Excel,
        [Description("Json")]
        Json,
        [Description("CSV")]
        CSV,
        [Description("XML")]
        XML,
        [Description("Migration")]
        Migration
    }
    #endregion

    #region Furnish Status
    public enum FurnishStatus
    {
        [Description("None")]
        None = 0,
        [Description("Unfurnished")]
        Unfurnished,
        [Description("Semifurnished")]
        Semifurnished,
        [Description("Furnished")]
        Furnished
    }
    #endregion

    #region Offer Type
    public enum OfferType
    {
        None = 0,
        [Description("Ready")]
        Ready,
        [Description("OffPlan")]
        OffPlan,
        [Description("Secondary")]
        Secondary
    }
    #endregion

    #region Bulk Type
    public enum BulkType
    {
        None = 0,
        [Description("bulk assignment")]
        BulkAssignment,
        [Description("bulk update status")]
        BulkUpdateStatus,
        [Description("bulk delete")]
        BulkDelete,
        [Description("bulk email")]
        BulkEmail,
        [Description("bulk source")]
        BulkSource,
        [Description("bulk project")]
        BulkProject,
        [Description("bulk whatsapp")]
        BulkWhatsApp,
        [Description("bulk upload")]
        BulkUpload,
        [Description("bulk migrate")]
        BulkMigrate,
        [Description("bulk secondary assignment")]
        BulkSecondaryAssignment
    }
    #endregion

    #region Lead Assignment Type
    public enum LeadAssignmentType
    {
        WithHistory = 0,
        WithoutHistory,
        WithoutHistoryWithNewStatus
    }
    #endregion

    #region Marital Status Type
    public enum MaritalStatusType
    {
        [Description("NotMentioned")]
        NotMentioned = 0,
        [Description("Single")]
        Single,
        [Description("Married")]
        Married
    }
    #endregion

    #region Gender
    public enum Gender
    {
        [Description("NotMentioned")]
        NotMentioned = 0,
        [Description("Male")]
        Male,
        [Description("Female")]
        Female,
        [Description("Other")]
        Other
    }
    #endregion

    #region Possesion Type
    public enum PossesionType
    {
        None,
        UnderConstruction,
        SixMonth,
        Year,
        TwoYears,
        CustomDate,
        Immediate
    }
    #endregion

    #region Purpose
    public enum Purpose
    {
        None = 0,
        [Description("Investment")]
        Investment,
        [Description("SelfUse")]
        SelfUse
    }
    #endregion

    #region Enum Description
    public static class EnumDescription
    {
        public static class LeadSource
        {
            public const string IVR = "IVR";
            public const string Facebook = "Facebook";
            public const string LinkedIn = "LinkedIn";
            public const string GoogleAds = "Google Ads";
            public const string MagicBricks = "Magic Bricks";
            public const string NinetyNineAcres = "99 Acres/Ninety Nine Acres";
            public const string Housing = "Housing.com";
            public const string GharOffice = "GharOffice";
            public const string Referral = "Referral";
            public const string WalkIn = "Walk In";
            public const string Website = "Website";
            public const string Direct = "Direct";
            public const string Gmail = "Gmail";
            public const string PropertyMicrosite = "Microsite";
            public const string PortfolioMicrosite = "Portfolio";
            public const string Phonebook = "Phonebook";
            public const string CallLogs = "Call Logs";
            public const string LeadPool = "Lead Pool";
            public const string SquareYards = "Square Yards";
            public const string JustLead = "Just Lead";
            public const string QuikrHomes = "Quikr Homes";
            public const string WhatsApp = "WhatsApp";
            public const string YouTube = "YouTube";
            public const string QRCode = "QR Code";
            public const string Instagram = "Instagram";
            public const string OLX = "OLX";
            public const string EstateDekho = "Estate Dekho";
            public const string GoogleSheet = "Google Sheets";
            public const string ChannelPartner = "Channel Partner";
            public const string RealEstateIndia = "Real Estate India";
            public const string CommonFloor = "Common Floor";
            public const string Data = "Data";
            public const string RoofandFloor = "Roof & Floor";
            public const string MicrosoftAds = "Microsoft Ads";
            public const string PropertyWala = "PropertyWala";
            public const string ProjectMicrosite = "Project Microsite";
            public const string MyGate = "MyGate";
            public const string Flipkart = "Flipkart";
            public const string PropertyFinder = "Property Finder";
            public const string Bayut = "Bayut";
            public const string Dubizzle = "Dubizzle";
            public const string Webhook = "Webhook";
            public const string TikTok = "TikTok";
            public const string Snapchat = "Snapchat";
            public const string GoogleAdsCampaign = "GoogleAdsCampaign";
        }

        public static class ErrorActionCode
        {
            public const string NoOp = "Do nothing.";
            public const string StayOn = "Stay on the same screen.";
            public const string ChangeRoute = "Navigate to any other screen, if needed.";
            public const string FallBack = "Navigate back to the parent screen.";
            public const string ReturnToHome = "Navigate to main/home screen.";
            public const string Refresh = "Refresh token data.";
            public const string Logout = "Log the user out.";
            public const string ShowToast = "Show toast.";
        }
        public static class MediaType
        {
            public const string None = "None";
            public const string Image = "Image";
            public const string Video = "Video";
            public const string Audio = "Audio";
            public const string PDF = "PDF";
        }
    }
    #endregion
}
